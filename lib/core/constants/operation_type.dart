import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';

enum OperationType { inbound, outbound, edit, add }

class ItemOperation {
  static Widget getOperationIcon(OperationType operationType) {
    switch (operationType) {
      case OperationType.inbound:
        return Container(
          width: 20.w,
          height: 20.h,
          decoration: BoxDecoration(
            color: AppColors.inBoundColor,
            shape: BoxShape.circle,
          ),
          alignment: Alignment.center,
          child: Text("入", style: TextStyle(color: Colors.white)),
        );
      case OperationType.outbound:
        return Container(
          width: 20.w,
          height: 20.h,
          decoration: BoxDecoration(
            color: AppColors.outBoundColor,
            shape: BoxShape.circle,
          ),
          alignment: Alignment.center,
          child: Text("出", style: TextStyle(color: Colors.white)),
        );
      case OperationType.edit:
        return Container(
          width: 20.w,
          height: 20.h,
          decoration: BoxDecoration(
            color: AppColors.editColor,
            shape: BoxShape.circle,
          ),
          alignment: Alignment.center,
          child: Icon(Icons.edit_rounded, color: Colors.white, size: 20.sp),
        );
      case OperationType.add:
        return Container(
          width: 20.w,
          height: 20.h,
          decoration: BoxDecoration(
            color: AppColors.addColor,
            shape: BoxShape.circle,
          ),
          alignment: Alignment.center,
          child: Icon(Icons.add_rounded, color: Colors.white, size: 20.sp),
        );
    }
  }
}
