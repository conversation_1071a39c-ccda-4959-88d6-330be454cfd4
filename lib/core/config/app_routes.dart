import 'package:flutter/material.dart';
import 'package:inventory_app_final/features/category_management/view/pages/category_management_page.dart';
import 'package:inventory_app_final/features/home/<USER>/pages/home_page.dart';
import 'package:inventory_app_final/features/items/view/pages/category_show_page.dart';
import 'package:inventory_app_final/features/outbound/view/pages/outbound_page.dart';
import 'package:inventory_app_final/features/search/view/pages/search_page.dart';
import 'package:inventory_app_final/features/supplier_management/view/pages/supplier_management_page.dart';
import 'package:inventory_app_final/features/warehouse_management/view/pages/warehouse_management_page.dart';

class AppRoutes {
  static const String search = 'search';
  static const String home = 'home';
  static const String outbound = 'outbound';
  static const String categoryManagement = 'category_management';
  static const String warehouseManagement = 'warehouse_management';
  static const String supplierManagement = 'supplier_management';
  static const String categoryShow = 'category_show';

  static Route<dynamic> generateRoute(RouteSettings routeSettings) {
    final String? routeName = routeSettings.name;

    switch (routeName) {
      case search:
        final args = routeSettings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (_) => SearchPage(
                initialSearchText: args['initialSearchText'] as String,
              ),
        );
      case outbound:
        return MaterialPageRoute(builder: (_) => OutboundPage());
      case categoryManagement:
        return MaterialPageRoute(builder: (_) => CategoryManagementPage());
      case warehouseManagement:
        return MaterialPageRoute(builder: (_) => WarehouseManagementPage());
      case supplierManagement:
        return MaterialPageRoute(builder: (_) => SupplierManagementPage());
      case categoryShow:
        return MaterialPageRoute(builder: (_) => CategoryShowPage());
      default:
        return MaterialPageRoute(builder: (_) => HomePage());
    }
  }
}
