import 'package:hive_ce/hive.dart';
import 'package:inventory_app_final/core/config/hive_adapters/hive_registrar.g.dart';
import 'package:inventory_app_final/core/constants/hive_box_name.dart';
import 'package:inventory_app_final/core/models/warehouse_model.dart';
import 'package:path_provider/path_provider.dart' as path_provider;

class HiveService {
  static Future<void> init() async {
    // 获取应用文档目录
    final appDocumentDirectory =
        await path_provider.getApplicationDocumentsDirectory();
    // 初始化Hive
    Hive.init(appDocumentDirectory.path);

    Hive.registerAdapters();
    // 这里可以提前打开全局必须的 Box
    await Hive.openBox<WarehouseModel>(HiveBoxName.warehouses);
    // 其他 Box 可以按需懒加载
  }

  static Box<WarehouseModel> get warehouseBox =>
      Hive.box(HiveBoxName.warehouses);

  /// 懒加载示例
  static Future<Box> getLazyBox(String boxName) async {
    if (Hive.isBoxOpen(boxName)) {
      return Hive.box(boxName);
    } else {
      return await Hive.openBox(boxName);
    }
  }
}
