import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomCircleIconButton extends StatelessWidget {
  final Color backgroundColor;
  final Color iconColor;
  final double? iconSize;
  final IconData icon;
  const CustomCircleIconButton({
    super.key,
    required this.backgroundColor,
    required this.iconColor,
    this.iconSize,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 20.w,
      height: 20.h,
      decoration: BoxDecoration(color: backgroundColor, shape: BoxShape.circle),
      alignment: Alignment.center,
      child: Icon(icon, color: iconColor, size: iconSize ?? 15.sp),
    );
  }
}
