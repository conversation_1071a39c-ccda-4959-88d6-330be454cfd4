import 'package:hive_ce/hive.dart';

@HiveType(typeId: 0)
class WarehouseModel extends HiveObject {
  @HiveField(0)
  final String id;
  @HiveField(1)
  final String name;
  @HiveField(2)
  final String address;
  @HiveField(3)
  final int totalItemAmount;
  @HiveField(4)
  final double totalItemMoney;
  @HiveField(5)
  bool isSelected;

  WarehouseModel({
    required this.id,
    required this.name,
    required this.address,
    required this.totalItemAmount,
    required this.totalItemMoney,
    this.isSelected = false,
  });

  factory WarehouseModel.fromJson(Map<String, dynamic> json) {
    return WarehouseModel(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      totalItemAmount: json['totalItemAmount'],
      totalItemMoney: json['totalItemMoney'],
      isSelected: json['isSelected'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'totalItemAmount': totalItemAmount,
      'totalItemMoney': totalItemMoney,
      'isSelected': isSelected,
    };
  }
}
