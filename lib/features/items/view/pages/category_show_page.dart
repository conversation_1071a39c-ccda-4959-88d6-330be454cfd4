import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/widgets/app_body.dart';

class CategoryShowPage extends StatefulWidget {
  const CategoryShowPage({super.key});

  @override
  State<CategoryShowPage> createState() => _CategoryShowPageState();
}

class _CategoryShowPageState extends State<CategoryShowPage> {
  List<Item> _items = [
    Item(title: '折叠项 1', content: '这里是内容 1'),
    Item(title: '折叠项 2', content: '这里是内容 2'),
    Item(title: '折叠项 3', content: '这里是内容 3'),
  ];

  @override
  Widget build(BuildContext context) {
    return AppBody(
      child: Scaffold(
        appBar: AppBar(
          title: Center(child: Text('选择分类', style: TextStyle(fontSize: 20.sp))),
        ),
        body: ListView(
          children:
              _items
                  .asMap()
                  .entries
                  .map((entry) => _buildExpandableTile(entry.key, entry.value))
                  .toList(),
        ),
      ),
    );
  }

  Widget _buildExpandableTile(int index, Item item) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          ListTile(
            title: Text(item.title),
            trailing: Icon(
              item.isExpanded
                  ? Icons.keyboard_arrow_up
                  : Icons.keyboard_arrow_down,
            ),
            onTap: () {
              setState(() {
                item.isExpanded = !item.isExpanded;
              });
            },
          ),
          AnimatedCrossFade(
            firstChild: Container(),
            secondChild: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(item.content),
            ),
            crossFadeState:
                item.isExpanded
                    ? CrossFadeState.showSecond
                    : CrossFadeState.showFirst,
            duration: const Duration(milliseconds: 300),
          ),
        ],
      ),
    );
  }
}

class Item {
  String title;
  String content;
  bool isExpanded;

  Item({required this.title, required this.content, this.isExpanded = false});
}
