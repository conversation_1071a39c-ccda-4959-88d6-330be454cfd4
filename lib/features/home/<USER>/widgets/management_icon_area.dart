import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/config/app_routes.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/core/widgets/custom_icon_button.dart';

class ManagementIconArea extends StatelessWidget {
  const ManagementIconArea({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.w),
        color: Colors.white,
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    CustomIconButton(
                      onTap: () {
                        FocusScope.of(context).unfocus();
                        Navigator.pushNamed(
                          context,
                          AppRoutes.categoryManagement,
                        );
                      },
                      icon: Icons.category_rounded,
                      size: 50.w,
                      iconColor: AppColors.outBoundColor,
                    ),
                    SizedBox(height: 3.h),
                    Text("分类管理"),
                  ],
                ),
                Column(
                  children: [
                    CustomIconButton(
                      icon: Icons.warehouse_rounded,
                      size: 50.w,
                      iconColor: AppColors.inBoundColor,
                      onTap: () {
                        FocusScope.of(context).unfocus();
                        Navigator.pushNamed(
                          context,
                          AppRoutes.warehouseManagement,
                        );
                      },
                    ),
                    SizedBox(height: 3.h),
                    Text("仓库管理"),
                  ],
                ),
                Column(
                  children: [
                    CustomIconButton(
                      icon: Icons.local_shipping_rounded,
                      size: 50.w,
                      iconColor: AppColors.addColor,
                      onTap: () {
                        FocusScope.of(context).unfocus();
                        Navigator.pushNamed(
                          context,
                          AppRoutes.supplierManagement,
                        );
                      },
                    ),
                    SizedBox(height: 3.h),
                    Text("供应商"),
                  ],
                ),
                Column(
                  children: [
                    CustomIconButton(
                      icon: Icons.people_rounded,
                      size: 50.w,
                      iconColor: AppColors.editColor,
                    ),
                    SizedBox(height: 3.h),
                    Text("用户管理"),
                  ],
                ),
              ],
            ),
            SizedBox(height: 10.h),
          ],
        ),
      ),
    );
  }
}
