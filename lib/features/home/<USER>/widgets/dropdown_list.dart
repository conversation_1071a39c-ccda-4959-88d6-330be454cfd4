import 'package:flutter/material.dart';

class DropdownList extends StatefulWidget {
  final List<String> addresses;
  final String initialAddress;
  final ValueChanged<String> onAddressChanged;
  final TextStyle? textStyle;

  const DropdownList({
    super.key,
    required this.addresses,
    required this.initialAddress,
    required this.onAddressChanged,
    this.textStyle,
  });

  @override
  State createState() => _DropdownListState();
}

class _DropdownListState extends State<DropdownList> {
  late String selectedAddress;

  @override
  void initState() {
    super.initState();
    selectedAddress = widget.initialAddress;
  }

  void _showPopupMenu(BuildContext context) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset.zero, ancestor: overlay),
        button.localToGlobal(
          button.size.bottomRight(Offset.zero),
          ancestor: overlay,
        ),
      ),
      Offset.zero & overlay.size,
    );

    String? selected = await showMenu<String>(
      context: context,
      position: position,
      items:
          widget.addresses.map((String address) {
            return PopupMenuItem<String>(value: address, child: Text(address));
          }).toList(),
    );

    if (selected != null && selected != selectedAddress) {
      setState(() {
        selectedAddress = selected;
      });
      widget.onAddressChanged(selected);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showPopupMenu(context),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            selectedAddress,
            style:
                widget.textStyle ??
                TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
            overflow: TextOverflow.ellipsis,
          ),
          const Icon(Icons.arrow_drop_down, color: Colors.black),
        ],
      ),
    );
  }
}
