// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'warehouse_local_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$warehouseLocalRepositoryHash() =>
    r'701185bbf764252714b62a6693c9b986030bd655';

/// See also [WarehouseLocalRepository].
@ProviderFor(WarehouseLocalRepository)
final warehouseLocalRepositoryProvider = AutoDisposeNotifierProvider<
  WarehouseLocalRepository,
  List<WarehouseModel>
>.internal(
  WarehouseLocalRepository.new,
  name: r'warehouseLocalRepositoryProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$warehouseLocalRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WarehouseLocalRepository = AutoDisposeNotifier<List<WarehouseModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
