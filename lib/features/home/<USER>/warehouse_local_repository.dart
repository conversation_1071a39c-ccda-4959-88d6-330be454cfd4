import 'package:hive_ce/hive.dart';
import 'package:inventory_app_final/core/models/warehouse_model.dart';
import 'package:inventory_app_final/core/services/hive_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uuid/uuid.dart';

part 'warehouse_local_repository.g.dart';

@riverpod
class WarehouseLocalRepository extends _$WarehouseLocalRepository {
  final _uuid = Uuid();
  final Box<WarehouseModel> _warehouseBox = HiveService.warehouseBox;

  @override
  List<WarehouseModel> build() {
    var warehouses = _warehouseBox.values.toList();
    if (warehouses.isEmpty) {
      WarehouseModel defaultWarehouse = WarehouseModel(
        id: _uuid.v4(),
        name: "默认仓库",
        address: "默认仓库地址",
        totalItemAmount: 0,
        totalItemMoney: 0.0,
      );
      _warehouseBox.add(defaultWarehouse);
      return [defaultWarehouse];
    }
    return warehouses;
  }

  Future<void> addWarehouse(WarehouseModel warehouse) async {
    final updated = [...state, warehouse];
    state = updated;
    await _warehouseBox.add(warehouse);
  }

  Future<void> removeWarehouse(WarehouseModel warehouse) async {
    final updated = state.where((w) => w != warehouse).toList();
    state = updated;
    await warehouse.delete();
  }

  Future<void> updateWarehouse(WarehouseModel warehouse) async {
    final index = state.indexWhere((w) => w.id == warehouse.id);
    if (index != -1) {
      final updated = [...state];
      updated[index] = warehouse;
      state = updated;
      // 自动覆盖到box
      await warehouse.save();
    }
  }

  Future<void> selectWarehouse(String warehouseId) async {
    // 1. 找到当前选中的仓库，取消选中
    final currentSelectedIndex = state.indexWhere((w) => w.isSelected);
    if (currentSelectedIndex != -1) {
      final currentSelected = state[currentSelectedIndex];
      currentSelected.isSelected = false;
      // 保存到box
      currentSelected.save();
    }

    // 2. 找到新选中的仓库，设为选中
    final newSelectedIndex = state.indexWhere((w) => w.id == warehouseId);
    if (newSelectedIndex != -1) {
      final newSelected = state[newSelectedIndex];
      newSelected.isSelected = true;
      // 保存到box
      newSelected.save();
    }
    state = [...state]; // 触发 Riverpod 状态更新
  }

  Future<WarehouseModel> getSelectedWarehouse() async {
    final selectedWarehouse = state.where((w) => w.isSelected).first;
    return selectedWarehouse;
  }
}
