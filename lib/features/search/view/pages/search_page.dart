import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/core/widgets/app_body.dart';
import 'package:inventory_app_final/core/widgets/custom_item_card.dart';
import 'package:inventory_app_final/core/widgets/search_box.dart';

class SearchPage extends StatefulWidget {
  final String initialSearchText;

  const SearchPage({super.key, required this.initialSearchText});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();

  final FocusNode _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return AppBody(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.scaffoldBackgroundColor,
          title: Center(
            child: Padding(
              padding: const EdgeInsets.only(right: 55),
              child: Text("搜索"),
            ),
          ),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 15.h),
            SearchBox(
              focusNode: _focusNode,
              controller: _searchController,
              onSearch: () {
                // TODO 更新本页面的搜索结果
              },
            ),
            SizedBox(height: 15.h),
            Text(
              "为你找到3条结果:${widget.initialSearchText}",
              style: TextStyle(fontSize: 13.sp),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [],
            ),
            SizedBox(height: 10.h),
            // TODO 这里后面要改成可滑动的懒加载列表
            CustomItemCard(
              name: "大宝贝乃哦好滴哦啊是的撒Asdlasjdasndiasdnas",
              englishName: "This is the english name",
              model: "MS-asd912",
              amount: '100',
              imageUrl: 'assets/images/test.jpg',
            ),
            CustomItemCard(
              name: "大宝贝乃哦好滴哦啊是的撒Asdlasjdasndiasdnas",
              englishName: "This is the english name",
              model: "MS-asd912",
              amount: '100',
              imageUrl: 'assets/images/test.jpg',
            ),
            CustomItemCard(
              name: "大宝贝乃哦好滴哦啊是的撒Asdlasjdasndiasdnas",
              englishName: "This is the english name",
              model: "MS-asd912",
              amount: '100',
              imageUrl: 'assets/images/test.jpg',
            ),
          ],
        ),
      ),
    );
  }
}
